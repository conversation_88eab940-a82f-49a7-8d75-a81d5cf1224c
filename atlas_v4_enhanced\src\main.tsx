import React from 'react';
import ReactDOM from 'react-dom/client';
import { useCreateChatClient } from 'stream-chat-react';

import AtlasApp from './components/AtlasApp';
import AtlasAppMinimal from './components/AtlasAppMinimal';
import AtlasAppSimple from './components/AtlasAppSimple';
import './styles/cyberpunk-theme.css';

// A.T.L.A.S. Stream Chat Configuration
const STREAM_API_KEY = import.meta.env.VITE_STREAM_API_KEY || 'demo-api-key';
const STREAM_USER_ID = import.meta.env.VITE_STREAM_USER_ID || 'atlas-user-1';
const STREAM_USER_NAME = import.meta.env.VITE_STREAM_USER_NAME || 'A.T.L.A.S. Trader';
const STREAM_USER_TOKEN = import.meta.env.VITE_STREAM_USER_TOKEN || 'demo-token';

// Error Boundary Component
class AtlasErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    console.error('🚨 A.T.L.A.S. Error Boundary caught error:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('🚨 A.T.L.A.S. Error Details:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{
          height: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 100%)',
          color: '#ffffff',
          fontFamily: 'monospace',
          flexDirection: 'column'
        }}>
          <h1 style={{ color: '#ff3366', fontSize: '2rem', marginBottom: '1rem' }}>
            A.T.L.A.S. Error
          </h1>
          <p style={{ color: '#8892b0', marginBottom: '1rem' }}>
            Something went wrong loading the interface.
          </p>
          <button
            onClick={() => window.location.reload()}
            style={{
              background: '#00ff88',
              color: '#000',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Reload A.T.L.A.S.
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// A.T.L.A.S. Main Application Wrapper
const AtlasMainApp: React.FC = () => {
  console.log('🚀 AtlasMainApp initializing...');

  // Initialize Stream Chat client if credentials are available
  let streamClient = null;
  try {
    streamClient = useCreateChatClient({
      apiKey: STREAM_API_KEY,
      tokenOrProvider: STREAM_USER_TOKEN,
      userData: {
        id: STREAM_USER_ID,
        name: STREAM_USER_NAME,
        image: `https://getstream.io/random_png/?name=${STREAM_USER_NAME}`,
      },
    });
    console.log('✅ Stream Chat client initialized successfully');
  } catch (error) {
    console.warn('⚠️ Stream Chat client failed to initialize:', error);
    streamClient = null;
  }

  console.log('🚀 A.T.L.A.S. v5.0 Enhanced - Modern Chat Interface Starting...');
  console.log('📊 Stream Chat Client:', streamClient ? 'Initialized' : 'Using Fallback Mode');
  console.log('🔧 Environment:', import.meta.env.MODE);

  // Switch back to full app for testing
  const useSimpleApp = false;
  const useMinimalApp = false;

  if (useSimpleApp) {
    console.log('🧪 Using AtlasAppSimple for testing...');
    return <AtlasAppSimple streamChatClient={streamClient || undefined} />;
  }

  if (useMinimalApp) {
    return <AtlasAppMinimal />;
  }

  return (
    <AtlasApp streamChatClient={streamClient || undefined} />
  );
};

// Initialize React Application
const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error('Root element not found');
}

const root = ReactDOM.createRoot(rootElement);

root.render(
  <React.StrictMode>
    <AtlasErrorBoundary>
      <AtlasMainApp />
    </AtlasErrorBoundary>
  </React.StrictMode>
);

// Development Hot Module Replacement
if (import.meta.hot) {
  import.meta.hot.accept();
}
