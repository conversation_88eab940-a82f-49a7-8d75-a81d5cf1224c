#!/usr/bin/env python3
"""
Test A.T.L.A.S. Interface Enhancements
Comprehensive testing of progress indicators, terminal streaming, and monitoring
"""

import asyncio
import json
import time
import websockets
import requests
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AtlasInterfaceEnhancementTester:
    """Test all interface enhancements"""
    
    def __init__(self, base_url="http://localhost:8001"):
        self.base_url = base_url
        self.ws_url = base_url.replace("http://", "ws://").replace("https://", "wss://")
        self.session_id = f"test_session_{int(time.time())}"
        
    async def test_websocket_connection(self):
        """Test WebSocket connection and real-time updates"""
        print("\n🔗 Testing WebSocket Connection...")
        
        try:
            uri = f"{self.ws_url}/ws/{self.session_id}"
            async with websockets.connect(uri) as websocket:
                print(f"✅ Connected to WebSocket: {uri}")
                
                # Wait for connection confirmation
                message = await websocket.recv()
                data = json.loads(message)
                
                if data.get("type") == "connection_established":
                    print(f"✅ Connection confirmed with features: {data.get('features', [])}")
                    
                    # Test sending a message to trigger progress updates
                    await self.test_chat_with_progress_tracking()
                    
                    # Listen for real-time updates for 10 seconds
                    print("📡 Listening for real-time updates...")
                    timeout = time.time() + 10
                    
                    while time.time() < timeout:
                        try:
                            message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                            data = json.loads(message)
                            
                            if data.get("type") == "progress_update":
                                print(f"📊 Progress Update: {data['operation']['title']} - {data['operation']['overall_progress']*100:.1f}%")
                            elif data.get("type") == "terminal_output":
                                print(f"💻 Terminal: {data['content']}")
                            elif data.get("type") == "log_message":
                                print(f"📝 Log [{data['level']}]: {data['content']}")
                            
                        except asyncio.TimeoutError:
                            continue
                    
                    print("✅ WebSocket test completed")
                    return True
                else:
                    print(f"❌ Unexpected connection response: {data}")
                    return False
                    
        except Exception as e:
            print(f"❌ WebSocket connection failed: {e}")
            return False
    
    async def test_chat_with_progress_tracking(self):
        """Test chat endpoint with progress tracking"""
        print("\n💬 Testing Chat with Progress Tracking...")
        
        try:
            response = requests.post(f"{self.base_url}/api/v1/chat", json={
                "message": "Analyze AAPL options trading opportunities",
                "session_id": self.session_id
            }, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Chat response received: {data['response'][:100]}...")
                print(f"📊 Confidence: {data['confidence']}")
                print(f"🏷️ Type: {data['type']}")
                return True
            else:
                print(f"❌ Chat request failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Chat test failed: {e}")
            return False
    
    def test_system_health_endpoint(self):
        """Test system health monitoring endpoint"""
        print("\n🏥 Testing System Health Endpoint...")
        
        try:
            response = requests.get(f"{self.base_url}/api/v1/system/health")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ System Status: {data['system_status']}")
                print(f"🕐 Timezone: {data['timezone']}")
                print(f"📈 Market Hours: {data['market_hours']}")
                
                # Check conversation monitoring
                conv_health = data.get('conversation_monitoring', {})
                print(f"💬 Active Sessions: {conv_health.get('active_sessions', 0)}")
                print(f"📊 Success Rate: {conv_health.get('success_rate', 0)}%")
                print(f"⚠️ Recent Alerts: {conv_health.get('recent_alerts', 0)}")
                
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Health check test failed: {e}")
            return False
    
    def test_monitoring_alerts_endpoint(self):
        """Test monitoring alerts endpoint"""
        print("\n🚨 Testing Monitoring Alerts Endpoint...")
        
        try:
            response = requests.get(f"{self.base_url}/api/v1/monitoring/alerts?hours=1")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Retrieved {data['total_alerts']} alerts from last {data['timeframe_hours']} hours")
                
                for alert in data['alerts'][:3]:  # Show first 3 alerts
                    print(f"⚠️ Alert: {alert['description']} [{alert['alert_level']}]")
                
                return True
            else:
                print(f"❌ Alerts request failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Alerts test failed: {e}")
            return False
    
    def test_central_time_configuration(self):
        """Test Central Time configuration"""
        print("\n🕐 Testing Central Time Configuration...")
        
        try:
            # Test market hours detection
            response = requests.get(f"{self.base_url}/api/v1/scanner/status")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Scanner Status: {data.get('status', 'unknown')}")
                
                # Check if market hours are correctly calculated for Central Time
                current_time = datetime.now()
                print(f"🕐 Current Time: {current_time.strftime('%H:%M:%S %Z')}")
                
                # Market should be open 8:30 AM - 3:00 PM CT
                market_hours = data.get('market_hours', False)
                print(f"📈 Market Hours Active: {market_hours}")
                
                return True
            else:
                print(f"❌ Scanner status request failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Central Time test failed: {e}")
            return False
    
    async def run_comprehensive_test(self):
        """Run all enhancement tests"""
        print("🚀 Starting A.T.L.A.S. Interface Enhancement Tests")
        print("=" * 60)
        
        results = {}
        
        # Test 1: System Health
        results['system_health'] = self.test_system_health_endpoint()
        
        # Test 2: Central Time Configuration
        results['central_time'] = self.test_central_time_configuration()
        
        # Test 3: Monitoring Alerts
        results['monitoring_alerts'] = self.test_monitoring_alerts_endpoint()
        
        # Test 4: WebSocket Connection and Real-time Updates
        results['websocket'] = await self.test_websocket_connection()
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name.replace('_', ' ').title()}: {status}")
        
        print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 All interface enhancements are working correctly!")
            print("\n🔧 ENHANCEMENTS VALIDATED:")
            print("• Real-time progress indicators")
            print("• Terminal output integration")
            print("• Central Time configuration (Houston, TX)")
            print("• WebSocket-based real-time updates")
            print("• Conversation monitoring system")
            print("• Enhanced system health reporting")
        else:
            print("⚠️ Some enhancements need attention.")
        
        return passed == total

async def main():
    """Main test function"""
    tester = AtlasInterfaceEnhancementTester()
    
    print("🔍 Testing A.T.L.A.S. Interface Enhancements")
    print("This will test all the new features including:")
    print("• Real-time progress indicators")
    print("• Terminal output streaming")
    print("• Central Time configuration")
    print("• Conversation monitoring")
    print("• WebSocket real-time updates")
    print()
    
    # Wait a moment for server to be ready
    print("⏳ Waiting for server to be ready...")
    await asyncio.sleep(2)
    
    success = await tester.run_comprehensive_test()
    
    if success:
        print("\n🎯 RECOMMENDATION: All enhancements are working correctly!")
        print("The A.T.L.A.S. system now provides:")
        print("• Detailed progress tracking for all operations")
        print("• Complete transparency with terminal output streaming")
        print("• Proper Central Time market hours (8:30 AM - 3:00 PM CT)")
        print("• Real-time conversation monitoring and quality assurance")
        print("• Enhanced user experience with WebSocket updates")
    else:
        print("\n⚠️ Some issues detected. Please check the server logs.")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
